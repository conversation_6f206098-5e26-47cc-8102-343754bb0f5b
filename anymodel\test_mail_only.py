#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
仅测试邮件获取功能
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_mail_retrieval():
    """测试邮件获取功能"""
    try:
        from anymodel_register import AnyModelRegistrar
        
        print("🧪 测试邮件获取功能")
        print("=" * 40)
        
        registrar = AnyModelRegistrar()
        
        print(f"\n📧 使用固定邮箱: {registrar.fixed_email}")
        print("🔍 开始检查邮件...")
        
        # 只检查一次邮件，不等待
        mail_id = registrar.get_mail_list(registrar.fixed_email, max_attempts=1, wait_seconds=0)
        
        if mail_id:
            print(f"\n✅ 找到邮件ID: {mail_id}")
            
            # 尝试获取邮件详情
            print("\n📄 获取邮件详情...")
            verification_code = registrar.get_mail_detail(registrar.fixed_email, mail_id)
            
            if verification_code:
                print(f"✅ 成功提取验证码: {verification_code}")
                print("\n🎉 邮件获取和验证码提取功能正常！")
                registrar.close()
                return True
            else:
                print("❌ 验证码提取失败")
        else:
            print("❌ 未找到邮件")
        
        registrar.close()
        return False
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("📧 AnyModel 邮件获取专项测试")
    print("=" * 50)
    print("💡 此测试将检查固定邮箱中是否有验证邮件")
    print("📬 邮箱: <EMAIL>")
    print("=" * 50)
    
    success = test_mail_retrieval()
    
    if success:
        print("\n🎉 邮件获取测试成功！")
        print("💡 现在可以运行完整的注册流程")
    else:
        print("\n⚠️ 邮件获取测试失败")
        print("💡 可能的原因:")
        print("   - 邮箱中暂时没有新邮件")
        print("   - 网络连接问题")
        print("   - tempmail.plus 服务问题")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
