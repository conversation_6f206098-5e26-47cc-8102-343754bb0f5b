#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试时间校验功能
"""

import sys
import os
from datetime import datetime, timedelta

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """主测试函数"""
    print("⏰ 测试邮件时间校验功能")
    print("=" * 40)
    
    try:
        from anymodel_register import AnyModelRegistrar
        
        print("🔧 创建注册器实例...")
        registrar = AnyModelRegistrar()
        
        print("\n📅 测试时间解析...")
        test_time = "2025-06-07 18:15:30"
        parsed = registrar.parse_mail_time(test_time)
        print(f"   输入: {test_time}")
        print(f"   解析: {parsed}")
        
        print("\n⏰ 测试时间校验...")
        # 设置注册开始时间
        registrar.registration_start_time = datetime.now()
        print(f"   注册时间: {registrar.registration_start_time}")
        
        # 测试当前时间的邮件
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        is_valid = registrar.is_mail_recent(current_time)
        print(f"   当前时间邮件: {current_time} -> {is_valid}")
        
        # 测试过旧的邮件
        old_time = (datetime.now() - timedelta(minutes=15)).strftime("%Y-%m-%d %H:%M:%S")
        is_valid_old = registrar.is_mail_recent(old_time)
        print(f"   15分钟前邮件: {old_time} -> {is_valid_old}")
        
        registrar.close()
        
        print("\n✅ 时间校验功能测试完成")
        print("💡 功能说明:")
        print("   - 脚本会记录注册开始时间")
        print("   - 只接受注册后10分钟内的验证邮件")
        print("   - 过旧的邮件会被自动过滤")
        print("   - 如有多封邮件，会选择最新的")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    print(f"\n🎯 测试结果: {'成功' if success else '失败'}")
