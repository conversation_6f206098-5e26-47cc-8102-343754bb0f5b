# AnyModel.xyz 自动注册脚本 - 修复版

## 🎉 问题已修复

原来的网络连接错误和 KeyboardInterrupt 问题已经完全修复！

## 🔧 修复内容

### 1. 网络连接优化
- ✅ 增加了连接超时配置
- ✅ 添加了自动重试机制（每个步骤最多重试3次）
- ✅ 优化了网络请求的错误处理

### 2. 中断处理优化
- ✅ 添加了 Ctrl+C 中断支持
- ✅ 批量注册时可以随时中断
- ✅ 中断后会显示已注册的账户数量

### 3. 错误处理增强
- ✅ 区分不同类型的网络错误
- ✅ 超时、连接错误、一般异常分别处理
- ✅ 详细的错误提示信息

## 🚀 使用方法

### 1. 安装依赖
```bash
pip install httpx
```

### 2. 运行脚本
```bash
python anymodel_register.py
```

### 3. 选择模式
- **模式1**: 注册单个账户（测试用）
- **模式2**: 批量注册账户

### 4. 批量注册参数
- **账户数量**: 要注册的总数
- **延迟时间**: 建议10-15秒（避免限流）

## 📋 运行示例

```
AnyModel.xyz 自动注册工具
==============================
提示: 按 Ctrl+C 可以随时中断操作

注册模式:
1. 注册单个账户
2. 批量注册账户

选择模式 (1-2): 2
要注册的账户数量: 3
请求间隔延迟 (秒, 建议10-15): 12

==================================================
正在注册第 1/3 个账户
==================================================
🚀 开始注册账户: <EMAIL>
🔄 注册尝试 1/3
注册请求状态: 201
✅ 注册成功: User created, check your email for verification code
🔍 等待验证邮件发送到: <EMAIL>
⏳ 尝试 1/10: 暂无邮件
等待 5 秒后重试...
✅ 找到验证邮件: Your AnyModel code
🔄 获取邮件详情尝试 1/3
✅ 提取到验证码: hzue2l5v
🔄 登录尝试 1/3
登录请求状态: 201
✅ 登录成功!
用户ID: 684400aa4d32f615a4af3c35
Token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
💾 账户已保存到 anymodel_accounts/anymodel_accounts_20250607.json (总计: 1 个)
✅ 第 1 个账户注册成功!
⏳ 等待 14.23 秒后进行下一次注册...
```

## 🛡️ 安全特性

### 网络重试机制
- 注册请求：最多重试3次
- 邮件获取：最多重试10次
- 邮件详情：最多重试3次
- 登录验证：最多重试3次

### 智能延迟
- 基础延迟 + 2-5秒随机延迟
- 避免触发网站限流
- 可以随时中断等待

### 错误恢复
- 网络超时自动重试
- 连接错误自动重试
- 详细的错误日志

## 📁 输出文件

成功注册的账户会保存到：
- 目录：`anymodel_accounts/`
- 文件：`anymodel_accounts_YYYYMMDD.json`

每个账户包含：
- 邮箱地址
- 密码
- 用户ID
- 访问Token
- 验证码

## ⚠️ 注意事项

1. **网络环境**: 确保网络连接稳定
2. **请求频率**: 建议延迟10-15秒避免限流
3. **中断操作**: 可以随时按 Ctrl+C 中断
4. **邮箱服务**: 依赖 tempmail.plus 的可用性

## 🔍 故障排除

### 如果遇到网络错误
- 脚本会自动重试3次
- 检查网络连接
- 尝试增加延迟时间

### 如果邮件获取失败
- 脚本会自动重试10次
- 检查 tempmail.plus 是否可访问
- 等待更长时间让邮件到达

### 如果需要中断
- 按 Ctrl+C 即可安全中断
- 已注册的账户会被保存
- 可以随时重新开始

## ✅ 测试通过

脚本已通过以下测试：
- ✅ 基础功能测试
- ✅ 网络连接测试  
- ✅ 中断处理测试
- ✅ 错误恢复测试

现在可以安全使用！
