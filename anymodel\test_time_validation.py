#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试邮件时间校验功能
"""

import sys
import os
from datetime import datetime, timedelta

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_time_parsing():
    """测试时间解析功能"""
    print("🧪 测试时间解析功能")
    print("=" * 40)
    
    try:
        from anymodel_register import AnyModelRegistrar
        registrar = AnyModelRegistrar()
        
        # 测试不同的时间格式
        test_times = [
            "2025-06-07 12:04:50",
            "2025-06-07 18:15:30",
            "2025-06-07 12:04",
            "invalid-time-format"
        ]
        
        for time_str in test_times:
            print(f"\n📅 测试时间: {time_str}")
            parsed_time = registrar.parse_mail_time(time_str)
            if parsed_time:
                print(f"✅ 解析成功: {parsed_time}")
            else:
                print(f"❌ 解析失败")
        
        registrar.close()
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        return False

def test_time_validation():
    """测试时间校验功能"""
    print("\n🧪 测试时间校验功能")
    print("=" * 40)
    
    try:
        from anymodel_register import AnyModelRegistrar
        registrar = AnyModelRegistrar()
        
        # 设置注册开始时间为当前时间
        registrar.registration_start_time = datetime.now()
        print(f"⏰ 模拟注册开始时间: {registrar.registration_start_time}")
        
        # 测试不同时间的邮件
        test_cases = [
            {
                "name": "当前时间的邮件",
                "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "expected": True
            },
            {
                "name": "5分钟前的邮件", 
                "time": (datetime.now() - timedelta(minutes=5)).strftime("%Y-%m-%d %H:%M:%S"),
                "expected": False
            },
            {
                "name": "2分钟后的邮件",
                "time": (datetime.now() + timedelta(minutes=2)).strftime("%Y-%m-%d %H:%M:%S"),
                "expected": True
            },
            {
                "name": "15分钟后的邮件",
                "time": (datetime.now() + timedelta(minutes=15)).strftime("%Y-%m-%d %H:%M:%S"),
                "expected": False
            }
        ]
        
        success_count = 0
        for case in test_cases:
            print(f"\n📧 测试: {case['name']}")
            print(f"   时间: {case['time']}")
            
            is_recent = registrar.is_mail_recent(case['time'])
            expected = case['expected']
            
            if is_recent == expected:
                print(f"✅ 校验正确: {is_recent}")
                success_count += 1
            else:
                print(f"❌ 校验错误: 期望={expected}, 实际={is_recent}")
        
        registrar.close()
        
        print(f"\n📊 测试结果: {success_count}/{len(test_cases)} 成功")
        return success_count == len(test_cases)
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mail_selection():
    """测试邮件选择逻辑"""
    print("\n🧪 测试邮件选择逻辑")
    print("=" * 40)
    
    # 模拟多封邮件的情况
    mock_mails = [
        {
            "mail_id": 1001,
            "subject": "Your AnyModel code",
            "from_mail": "<EMAIL>",
            "time": (datetime.now() - timedelta(minutes=5)).strftime("%Y-%m-%d %H:%M:%S")
        },
        {
            "mail_id": 1002, 
            "subject": "Your AnyModel code",
            "from_mail": "<EMAIL>",
            "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        },
        {
            "mail_id": 1003,
            "subject": "Your AnyModel code", 
            "from_mail": "<EMAIL>",
            "time": (datetime.now() + timedelta(minutes=2)).strftime("%Y-%m-%d %H:%M:%S")
        }
    ]
    
    print("📧 模拟邮件列表:")
    for mail in mock_mails:
        print(f"   ID={mail['mail_id']}, 时间={mail['time']}")
    
    print("\n💡 预期结果: 应该选择最新的有效邮件 (ID=1003)")
    
    return True

def main():
    """主测试函数"""
    print("⏰ AnyModel 邮件时间校验测试")
    print("=" * 50)
    
    tests = [
        ("时间解析", test_time_parsing),
        ("时间校验", test_time_validation),
        ("邮件选择", test_mail_selection),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 测试: {test_name}")
        print("-" * 30)
        if test_func():
            passed += 1
            print(f"✅ {test_name} 测试通过")
        else:
            print(f"❌ {test_name} 测试失败")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有时间校验功能测试通过！")
        print("\n📝 功能说明:")
        print("✅ 邮件时间解析正常")
        print("✅ 时间校验逻辑正确")
        print("✅ 会自动选择最新的有效验证邮件")
        print("✅ 过旧的邮件会被过滤掉")
        
        print("\n💡 使用建议:")
        print("- 脚本会自动记录注册开始时间")
        print("- 只接受注册后10分钟内的验证邮件")
        print("- 如有多封验证邮件，自动选择最新的")
    else:
        print("❌ 部分测试失败，请检查时间校验逻辑")
        
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
