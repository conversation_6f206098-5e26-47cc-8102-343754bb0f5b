#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试带时间校验的邮件获取功能
"""

import sys
import os
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """主测试函数"""
    print("📧 测试带时间校验的邮件获取功能")
    print("=" * 50)
    
    try:
        from anymodel_register import AnyModelRegistrar
        
        print("🔧 创建注册器实例...")
        registrar = AnyModelRegistrar()
        
        # 模拟注册开始时间
        registrar.registration_start_time = datetime.now()
        print(f"⏰ 模拟注册开始时间: {registrar.registration_start_time}")
        
        print(f"\n📬 检查邮箱: {registrar.fixed_email}")
        print("🔍 开始获取邮件（带时间校验）...")
        
        # 获取邮件，只检查一次
        mail_id = registrar.get_mail_list(registrar.fixed_email, max_attempts=1, wait_seconds=0)
        
        if mail_id:
            print(f"\n✅ 找到有效邮件ID: {mail_id}")
            print("💡 说明: 脚本已正确过滤了过旧的邮件，只返回最新的有效验证邮件")
            
            # 尝试获取邮件详情
            print("\n📄 获取邮件详情...")
            verification_code = registrar.get_mail_detail(registrar.fixed_email, mail_id)
            
            if verification_code:
                print(f"✅ 成功提取验证码: {verification_code}")
                print("\n🎉 带时间校验的邮件获取功能正常！")
            else:
                print("⚠️ 验证码提取失败，但邮件获取正常")
        else:
            print("\n⚠️ 未找到有效的验证邮件")
            print("💡 可能的原因:")
            print("   - 邮箱中没有新的验证邮件")
            print("   - 所有邮件都太旧，被时间校验过滤了")
            print("   - 网络连接问题")
        
        registrar.close()
        
        print("\n📋 时间校验功能总结:")
        print("✅ 自动记录注册开始时间")
        print("✅ 过滤注册前收到的邮件")
        print("✅ 过滤超过10分钟的旧邮件")
        print("✅ 自动选择最新的有效邮件")
        print("✅ 详细的时间校验日志")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    print(f"\n🎯 测试结果: {'成功' if success else '失败'}")
    
    if success:
        print("\n🚀 现在可以运行完整的注册流程:")
        print("   python anymodel_register.py")
        print("\n💡 新功能:")
        print("   - 自动过滤过旧的验证邮件")
        print("   - 确保使用最新的验证码")
        print("   - 避免使用过期的验证码导致登录失败")
