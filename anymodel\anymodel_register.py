#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AnyModel.xyz 自动注册脚本
简化版本，专门用于 AnyModel.xyz 注册
"""

import json
import random
import string
import time
import re
from pathlib import Path
from datetime import datetime
from urllib.parse import quote

try:
    import httpx
except ImportError:
    print("❌ 缺少 httpx 库，请运行: pip install httpx")
    exit(1)

class AnyModelRegistrar:
    """AnyModel.xyz 自动注册器"""
    
    def __init__(self):
        self.base_url = "https://app.anymodel.xyz"
        self.tempmail_url = "https://tempmail.plus"
        self.session = httpx.Client(timeout=30.0, verify=False)
        
    def generate_random_email(self, length=6):
        """生成随机邮箱地址"""
        letters = string.ascii_lowercase
        username = ''.join(random.choice(letters) for _ in range(length))
        return f"{username}@kiechck.top"
    
    def get_signup_headers(self):
        """获取注册请求头"""
        return {
            'accept': 'application/json',
            'accept-language': 'zh-CN,zh;q=0.9',
            'content-type': 'application/json',
            'origin': 'https://app.anymodel.xyz',
            'priority': 'u=1, i',
            'referer': 'https://app.anymodel.xyz/',
            'sec-ch-ua': '"Not:A-Brand";v="24", "Chromium";v="134", "Google Chrome";v="134"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }
    
    def get_tempmail_headers(self, email):
        """获取临时邮箱请求头"""
        return {
            'accept': 'application/json, text/javascript, */*; q=0.01',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'priority': 'u=1, i',
            'referer': 'https://tempmail.plus/zh/',
            'sec-ch-ua': '"Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'x-requested-with': 'XMLHttpRequest'
        }
    
    def signup(self, email, password):
        """发起注册"""
        url = f"{self.base_url}/api/signup"
        headers = self.get_signup_headers()
        
        data = {
            "email": email,
            "password": password,
            "emailConsent": True,
            "analyticsConsent": True
        }
        
        try:
            response = self.session.post(url, headers=headers, json=data)
            print(f"注册请求状态: {response.status_code}")
            
            if response.status_code == 201:
                result = response.json()
                print(f"✅ 注册成功: {result.get('msg', '未知消息')}")
                return True
            else:
                print(f"❌ 注册失败: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 注册请求异常: {e}")
            return False
    
    def get_mail_list(self, email, max_attempts=10, wait_seconds=5):
        """获取邮件列表，等待验证邮件"""
        encoded_email = quote(email)
        url = f"{self.tempmail_url}/api/mails?email={encoded_email}&first_id=0&epin="
        headers = self.get_tempmail_headers(email)
        
        print(f"🔍 等待验证邮件发送到: {email}")
        
        for attempt in range(max_attempts):
            try:
                response = self.session.get(url, headers=headers)
                
                if response.status_code == 200:
                    data = response.json()
                    
                    if data.get('result') and data.get('count', 0) > 0:
                        mail_list = data.get('mail_list', [])
                        
                        # 查找来自 AnyModel 的邮件
                        for mail in mail_list:
                            subject = mail.get('subject', '').lower()
                            from_mail = mail.get('from_mail', '').lower()
                            
                            if 'anymodel' in subject or 'anymodel' in from_mail or 'verification' in subject:
                                print(f"✅ 找到验证邮件: {mail.get('subject', '无主题')}")
                                return mail.get('mail_id')
                        
                        print(f"📧 收到 {len(mail_list)} 封邮件，但没有找到 AnyModel 验证邮件")
                    else:
                        print(f"⏳ 尝试 {attempt + 1}/{max_attempts}: 暂无邮件")
                else:
                    print(f"❌ 获取邮件列表失败: {response.status_code}")
                
                if attempt < max_attempts - 1:
                    print(f"等待 {wait_seconds} 秒后重试...")
                    time.sleep(wait_seconds)
                    
            except Exception as e:
                print(f"❌ 获取邮件列表异常: {e}")
                if attempt < max_attempts - 1:
                    time.sleep(wait_seconds)
        
        print("❌ 超时未收到验证邮件")
        return None
    
    def get_mail_detail(self, email, mail_id):
        """获取邮件详情并提取验证码"""
        encoded_email = quote(email)
        url = f"{self.tempmail_url}/api/mails/{mail_id}?email={encoded_email}&epin="
        headers = self.get_tempmail_headers(email)
        
        try:
            response = self.session.get(url, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                
                if data.get('result'):
                    # 从邮件内容中提取验证码
                    text_content = data.get('text', '')
                    html_content = data.get('html', '')
                    
                    # 尝试从文本和HTML中提取验证码
                    verification_code = self.extract_verification_code(text_content) or \
                                      self.extract_verification_code(html_content)
                    
                    if verification_code:
                        print(f"✅ 提取到验证码: {verification_code}")
                        return verification_code
                    else:
                        print("❌ 未能从邮件中提取验证码")
                        print(f"邮件文本内容: {text_content}")
                        print(f"邮件HTML内容: {html_content}")
                else:
                    print("❌ 邮件详情获取失败")
            else:
                print(f"❌ 获取邮件详情失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 获取邮件详情异常: {e}")
        
        return None
    
    def extract_verification_code(self, content):
        """从邮件内容中提取验证码"""
        if not content:
            return None
        
        # 尝试多种验证码模式
        patterns = [
            r'verification code is (\w+)',
            r'验证码[：:]\s*(\w+)',
            r'code[：:]\s*(\w+)',
            r'验证码为[：:]\s*(\w+)',
            r'(\w{8})',  # 8位字母数字组合
            r'([a-z0-9]{6,10})',  # 6-10位字母数字组合
        ]
        
        for pattern in patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                code = match.group(1)
                # 验证码通常是6-10位的字母数字组合
                if 6 <= len(code) <= 10 and code.isalnum():
                    return code
        
        return None
    
    def login_with_verification(self, email, password, verification_code):
        """使用验证码登录"""
        url = f"{self.base_url}/api/login"
        headers = self.get_signup_headers()
        
        data = {
            "provider": "AnyModel",
            "credentials": {
                "email": email,
                "password": password
            },
            "verificationCode": verification_code
        }
        
        try:
            response = self.session.post(url, headers=headers, json=data)
            print(f"登录请求状态: {response.status_code}")
            
            if response.status_code == 201:
                result = response.json()
                token = result.get('token')
                user_id = result.get('id')
                
                if token:
                    print(f"✅ 登录成功!")
                    print(f"用户ID: {user_id}")
                    print(f"Token: {token}")
                    
                    return {
                        'email': email,
                        'password': password,
                        'user_id': user_id,
                        'token': token,
                        'verification_code': verification_code
                    }
                else:
                    print("❌ 登录成功但未获取到token")
            else:
                print(f"❌ 登录失败: {response.text}")
                
        except Exception as e:
            print(f"❌ 登录请求异常: {e}")
        
        return None
    
    def register_single_account(self):
        """注册单个账户的完整流程"""
        # 1. 生成随机邮箱
        email = self.generate_random_email()
        password = email  # 使用邮箱作为密码
        
        print(f"🚀 开始注册账户: {email}")
        
        # 2. 发起注册
        if not self.signup(email, password):
            return None
        
        # 3. 等待并获取验证邮件
        mail_id = self.get_mail_list(email)
        if not mail_id:
            return None
        
        # 4. 获取邮件详情并提取验证码
        verification_code = self.get_mail_detail(email, mail_id)
        if not verification_code:
            return None
        
        # 5. 使用验证码登录
        account_info = self.login_with_verification(email, password, verification_code)
        
        return account_info
    
    def close(self):
        """关闭会话"""
        self.session.close()

def save_account(account_data):
    """保存账户数据"""
    output_dir = Path("anymodel_accounts")
    output_dir.mkdir(exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d")
    output_file = output_dir / f"anymodel_accounts_{timestamp}.json"
    
    # 读取现有数据
    existing_accounts = []
    if output_file.exists():
        try:
            with open(output_file, "r", encoding="utf-8") as f:
                existing_accounts = json.load(f)
        except:
            existing_accounts = []
    
    # 添加新账户
    existing_accounts.append(account_data)
    
    # 保存回文件
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(existing_accounts, f, indent=2, ensure_ascii=False)
    
    print(f"💾 账户已保存到 {output_file} (总计: {len(existing_accounts)} 个)")

def main():
    """主函数"""
    print("AnyModel.xyz 自动注册工具")
    print("=" * 30)
    
    print("\n注册模式:")
    print("1. 注册单个账户")
    print("2. 批量注册账户")
    
    while True:
        try:
            mode = int(input("\n选择模式 (1-2): "))
            if mode in [1, 2]:
                break
            else:
                print("请输入1或2。")
        except ValueError:
            print("请输入有效数字。")
    
    if mode == 1:
        # 单个账户注册
        registrar = AnyModelRegistrar()
        try:
            account_info = registrar.register_single_account()
            if account_info:
                save_account(account_info)
                print("\n🎉 注册完成!")
            else:
                print("\n❌ 注册失败")
        finally:
            registrar.close()
    
    elif mode == 2:
        # 批量注册
        count = int(input("要注册的账户数量: "))
        delay_seconds = float(input("请求间隔延迟 (秒, 建议10-15): "))
        
        registrar = AnyModelRegistrar()
        successful_accounts = []
        
        try:
            for i in range(count):
                print(f"\n{'='*50}")
                print(f"正在注册第 {i+1}/{count} 个账户")
                print(f"{'='*50}")
                
                account_info = registrar.register_single_account()
                
                if account_info:
                    successful_accounts.append(account_info)
                    save_account(account_info)
                    print(f"✅ 第 {i+1} 个账户注册成功!")
                else:
                    print(f"❌ 第 {i+1} 个账户注册失败")
                
                # 延迟
                if i < count - 1:
                    random_extra = round(random.uniform(2.0, 5.0), 2)
                    total_delay = delay_seconds + random_extra
                    print(f"⏳ 等待 {total_delay:.2f} 秒后进行下一次注册...")
                    time.sleep(total_delay)
        
        finally:
            registrar.close()
        
        print(f"\n🎉 批量注册完成!")
        print(f"成功注册: {len(successful_accounts)} 个账户")
        print(f"失败: {count - len(successful_accounts)} 个账户")

if __name__ == "__main__":
    main()
