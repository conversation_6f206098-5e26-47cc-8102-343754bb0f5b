#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的 AnyModel 注册脚本
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from anymodel_register import AnyModelRegistrar
    print("✅ 成功导入 AnyModelRegistrar")
    
    # 测试创建实例
    registrar = AnyModelRegistrar()
    print("✅ 成功创建 AnyModelRegistrar 实例")
    
    # 测试生成邮箱
    email = registrar.generate_random_email()
    print(f"✅ 成功生成邮箱: {email}")
    
    # 测试关闭会话
    registrar.close()
    print("✅ 成功关闭会话")
    
    print("\n🎉 所有基础功能测试通过！")
    print("脚本已修复，可以正常使用。")
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
except Exception as e:
    print(f"❌ 测试过程中出现错误: {e}")
