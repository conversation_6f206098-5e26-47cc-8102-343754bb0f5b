#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的详细日志版本
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from anymodel_register import AnyModelRegistrar
    print("✅ 成功导入 AnyModelRegistrar")
    
    # 测试创建实例
    print("\n" + "="*50)
    print("🧪 开始测试详细日志功能")
    print("="*50)
    
    registrar = AnyModelRegistrar()
    print("\n✅ 成功创建 AnyModelRegistrar 实例")
    
    # 测试生成邮箱
    email = registrar.generate_random_email()
    print(f"✅ 成功生成邮箱: {email}")
    
    # 测试固定邮箱
    print(f"✅ 固定监听邮箱: {registrar.fixed_email}")
    
    # 测试关闭会话
    registrar.close()
    print("✅ 成功关闭会话")
    
    print("\n🎉 所有基础功能测试通过！")
    print("📝 详细日志功能已启用")
    print("📧 固定邮箱已配置: <EMAIL>")
    print("🔒 SSL连接问题已修复")
    
    print("\n💡 现在可以运行完整注册测试:")
    print("   python anymodel_register.py")
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
except Exception as e:
    print(f"❌ 测试过程中出现错误: {e}")
    import traceback
    traceback.print_exc()
