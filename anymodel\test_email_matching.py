#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试邮件匹配和验证码提取功能
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_email_matching():
    """测试邮件匹配逻辑"""
    print("🧪 测试邮件匹配逻辑")
    print("=" * 40)
    
    # 模拟你收到的邮件数据
    test_mail = {
        'mail_id': 3347301430,
        'subject': 'Your AnyModel code',
        'from_mail': 'SRS0=E3hy=wy=munro-research.com=<EMAIL>',
        'from_name': '',
    }
    
    print(f"📧 测试邮件:")
    print(f"   ID: {test_mail['mail_id']}")
    print(f"   主题: {test_mail['subject']}")
    print(f"   发件人: {test_mail['from_mail']}")
    
    # 测试匹配逻辑
    subject = test_mail['subject'].lower()
    from_mail = test_mail['from_mail'].lower()
    
    print(f"\n🔍 匹配检查:")
    print(f"   anymodel in subject: {'anymodel' in subject}")
    print(f"   anymodel in from_mail: {'anymodel' in from_mail}")
    print(f"   verification in subject: {'verification' in subject}")
    print(f"   munro-research.com in from_mail: {'munro-research.com' in from_mail}")
    print(f"   code in subject: {'code' in subject}")
    print(f"   noreply in from_mail: {'noreply' in from_mail}")
    
    # 新的匹配逻辑
    is_anymodel_mail = (
        'anymodel' in subject or 
        'anymodel' in from_mail or 
        'verification' in subject or
        'munro-research.com' in from_mail or
        'code' in subject or
        'noreply' in from_mail
    )
    
    print(f"\n✅ 匹配结果: {is_anymodel_mail}")
    
    if is_anymodel_mail:
        print("🎉 邮件匹配成功！新逻辑可以正确识别此邮件")
    else:
        print("❌ 邮件匹配失败！需要进一步调整匹配逻辑")
    
    return is_anymodel_mail

def test_verification_code_extraction():
    """测试验证码提取功能"""
    print("\n🧪 测试验证码提取功能")
    print("=" * 40)
    
    # 模拟邮件内容
    test_contents = [
        "Your AnyModel verification code is qq83uw7t",
        "Your AnyModel ( https://app.anymodel.xyz/ ) verification code is qq83uw7t",
        "verification code is qq83uw7t",
        "code is qq83uw7t",
        "Your code: qq83uw7t",
        "验证码：qq83uw7t",
    ]
    
    try:
        from anymodel_register import AnyModelRegistrar
        registrar = AnyModelRegistrar()
        
        success_count = 0
        for i, content in enumerate(test_contents):
            print(f"\n📝 测试内容 {i+1}: {content}")
            code = registrar.extract_verification_code(content)
            if code == "qq83uw7t":
                print(f"✅ 提取成功: {code}")
                success_count += 1
            else:
                print(f"❌ 提取失败: {code}")
        
        registrar.close()
        
        print(f"\n📊 测试结果: {success_count}/{len(test_contents)} 成功")
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 AnyModel 邮件匹配和验证码提取测试")
    print("=" * 50)
    
    # 测试邮件匹配
    email_match_success = test_email_matching()
    
    # 测试验证码提取
    code_extract_success = test_verification_code_extraction()
    
    print("\n" + "=" * 50)
    print("📋 测试总结:")
    print(f"   📧 邮件匹配: {'✅ 通过' if email_match_success else '❌ 失败'}")
    print(f"   🔐 验证码提取: {'✅ 通过' if code_extract_success else '❌ 失败'}")
    
    if email_match_success and code_extract_success:
        print("\n🎉 所有测试通过！")
        print("💡 现在脚本应该能够正确:")
        print("   - 识别来自 munro-research.com 的邮件")
        print("   - 匹配 'Your AnyModel code' 主题")
        print("   - 提取 qq83uw7t 格式的验证码")
        print("\n🚀 可以重新运行注册脚本测试")
    else:
        print("\n⚠️ 部分测试失败，需要进一步调整")
    
    return email_match_success and code_extract_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
