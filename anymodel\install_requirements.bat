@echo off
echo 正在安装 AnyModel.xyz 注册脚本所需的依赖...
echo.

echo 检查 Python 是否已安装...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到 Python，请先安装 Python 3.7+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Python 已安装，版本信息:
python --version

echo.
echo 正在安装依赖库...
pip install httpx
if %errorlevel% neq 0 (
    echo 错误: 安装 httpx 失败
    pause
    exit /b 1
)

echo.
echo ✅ 依赖安装完成！
echo.
echo 现在可以运行注册脚本了:
echo   python anymodel_register.py
echo.
pause
